// atropos-frontend-desktop/src/pages/DashboardPage.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Heading, Text, Button, VStack, Center } from '@chakra-ui/react';
// Chakra UI kullandığımız için artık App.css'i burada doğrudan import etmiyoruz.

function DashboardPage() {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('accessToken'); // Token'ı sil
    navigate('/login'); // Giriş sayfasına yönlendir
  };

  return (
    <Center minH="100vh" bg="gray.50">
      <Box
        p={8}
        maxWidth="600px"
        borderWidth={1}
        borderRadius="lg"
        boxShadow="lg"
        bg="white"
      >
        <VStack spacing={4}>
          <Heading as="h2" size="lg">
            Hoş Geldiniz!
          </Heading>
          <Text fontSize="md">
            Atropos POS Sistemine başarıyla giriş yaptınız. Burası ana kontrol paneliniz olacak.
          </Text>
          <Button colorScheme="red" onClick={handleLogout}>
            Çıkış Yap
          </Button>
        </VStack>
      </Box>
    </Center>
  );
}

export default DashboardPage;
