// atropos-frontend-desktop/src/pages/LoginPage/LoginPage.tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Input,
  Text,
  VStack,
  Heading,
  Center,
} from '@chakra-ui/react';
// Chakra UI kullandığımız için artık App.css'i burada doğrudan import etmiyoruz.
// Genel stiller main.tsx veya App.css'te global olarak yönetilebilir.

interface LoginResponse {
  accessToken: string;
}

function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; //

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`${API_URL}/auth/login`, { //
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data: LoginResponse & { message?: string } = await response.json();

      if (response.ok) {
        localStorage.setItem('accessToken', data.accessToken);
        console.log('Access Token:', data.accessToken);
        alert('Giriş Başarılı! Panele yönlendiriliyorsunuz.');
        navigate('/dashboard');
      } else {
        alert(data.message || 'Kullanıcı adı veya parola hatalı.');
      }
    } catch (error) {
      console.error('Login sırasında hata oluştu:', error);
      alert('Bağlantı Hatası: Sunucuya ulaşılamıyor veya ağ bağlantınızda sorun var.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center minH="100vh" bg="gray.50">
      <Box
        p={8}
        maxWidth="400px"
        borderWidth={1}
        borderRadius="lg"
        boxShadow="lg"
        bg="white"
      >
        <VStack spacing={6}>
          <Heading as="h1" size="lg" mb={4}>
            Atropos POS - Giriş
          </Heading>
          <form onSubmit={handleLogin} style={{ width: '100%' }}>
            <VStack spacing={4}>
              <Box width="100%">
                <Text mb={2} fontWeight="medium">Kullanıcı Adı</Text>
                <Input
                  type="text"
                  placeholder="Kullanıcı adınızı girin"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </Box>
              <Box width="100%">
                <Text mb={2} fontWeight="medium">Parola</Text>
                <Input
                  type="password"
                  placeholder="Parolanızı girin"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </Box>
              <Button
                type="submit"
                colorScheme="blue"
                width="full"
                disabled={isLoading}
              >
                {isLoading ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
              </Button>
            </VStack>
          </form>
        </VStack>
      </Box>
    </Center>
  );
}

export default LoginPage;
