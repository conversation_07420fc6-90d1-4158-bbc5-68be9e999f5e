// atropos-frontend-desktop/src/pages/LoginPage/LoginPage.tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Input,
  Text,
  VStack,
  Heading,
  Center,
} from '@chakra-ui/react';

interface LoginResponse {
  accessToken: string;
}

function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; //

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`${API_URL}/auth/login`, { //
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data: LoginResponse & { message?: string } = await response.json();

      if (response.ok) {
        localStorage.setItem('accessToken', data.accessToken);
        console.log('Access Token:', data.accessToken);

        toast({
          title: 'Giriş Başarılı!',
          description: 'Panele yönlendiriliyorsunuz...',
          status: 'success',
          duration: 2000,
          isClosable: true,
        });

        // Kısa bir gecikme ile kullanıcı deneyimini iyileştir
        setTimeout(() => {
          navigate('/dashboard');
        }, 1000);
      } else {
        toast({
          title: 'Giriş Başarısız',
          description: data.message || 'Kullanıcı adı veya parola hatalı.',
          status: 'error',
          duration: 4000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Login sırasında hata oluştu:', error);
      toast({
        title: 'Bağlantı Hatası',
        description: 'Sunucuya ulaşılamıyor. Lütfen ağ bağlantınızı kontrol edin.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Center minH="100vh" bg="gray.50">
      <Box
        p={8}
        maxWidth="420px"
        w="full"
        mx={4}
        borderWidth={1}
        borderRadius="xl"
        boxShadow="xl"
        bg="white"
      >
        <VStack spacing={6}>
          <Heading
            as="h1"
            size="lg"
            color="brand.700"
            textAlign="center"
            mb={2}
          >
            Atropos POS
          </Heading>
          <Text
            fontSize="md"
            color="gray.600"
            textAlign="center"
            mb={4}
          >
            Sisteme giriş yapın
          </Text>

          <form onSubmit={handleLogin} style={{ width: '100%' }}>
            <VStack spacing={5}>
              <FormControl isRequired>
                <FormLabel color="gray.700" fontWeight="medium">
                  Kullanıcı Adı
                </FormLabel>
                <Input
                  type="text"
                  placeholder="Kullanıcı adınızı girin"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={isLoading}
                  size="lg"
                  focusBorderColor="brand.500"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel color="gray.700" fontWeight="medium">
                  Parola
                </FormLabel>
                <InputGroup size="lg">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Parolanızı girin"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    focusBorderColor="brand.500"
                  />
                  <InputRightElement>
                    <IconButton
                      aria-label={showPassword ? 'Parolayı gizle' : 'Parolayı göster'}
                      icon={showPassword ? <ViewOffIcon /> : <ViewIcon />}
                      onClick={() => setShowPassword(!showPassword)}
                      variant="ghost"
                      size="sm"
                      disabled={isLoading}
                    />
                  </InputRightElement>
                </InputGroup>
              </FormControl>

              <Button
                type="submit"
                colorScheme="brand"
                size="lg"
                width="full"
                isLoading={isLoading}
                loadingText="Giriş Yapılıyor..."
                mt={4}
              >
                Giriş Yap
              </Button>
            </VStack>
          </form>
        </VStack>
      </Box>
    </Center>
  );
}

export default LoginPage;
