/* atropos-frontend-desktop/src/App.css */

#root {
  max-width: 1280px; /* <PERSON><PERSON> değer Figma tasarımınıza göre ayarlanabilir, merkezi konteyner için bir max-width */
  margin: 0 auto;
  padding: 0; /* Chakra kendi paddingini yönetecek */
  text-align: center;
  min-height: 100vh; /* Uygulamanın en az ekran yüksekliği kadar olmasını sağlar */
  display: flex; /* Flexbox ile içerik ortalama için */
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

body {
    margin: 0;
    /* Chakra UI kendi reset stillerini uygulayacağı için burada başka global stil tutmaya gerek yok. */
}

/* Eğer App bileşeni içinde hala '.App' sınıfını kullanıyorsak ve dış konteyner olarak işlev görüyorsa */
.App {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
