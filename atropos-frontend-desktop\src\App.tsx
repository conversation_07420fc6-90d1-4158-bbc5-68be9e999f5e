// atropos-frontend-desktop/src/App.tsx
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from './pages/LoginPage/LoginPage'; // Artık klasör içinden alıyoruz
import DashboardPage from './pages/DashboardPage';
import './App.css'; // Global stiller için mevcut CSS'i koruyoruz

function App() {
  const isAuthenticated = localStorage.getItem('accessToken') ? true : false;

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route
          path="/dashboard"
          element={isAuthenticated ? <DashboardPage /> : <Navigate to="/login" replace />}
        />
        <Route
          path="/"
          element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />}
        />
        <Route path="*" element={<div>404 - Sayfa Bulunamadı</div>} />
      </Routes>
    </Router>
  );
}

export default App;
